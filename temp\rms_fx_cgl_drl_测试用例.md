# RMS单次常规用量分析存储过程测试用例

## 存储过程概述

**存储过程名称**: `rms_fx_cgl_drl`  
**功能描述**: 单次常规用量分析存储过程，用于检查药品的给药频次是否符合医院自定义频次或药品说明书推荐频次  
**当前大模型**: Claude Sonnet 4

### 输入参数
- `p_code`: 处方编码 (VARCHAR(50))
- `p_akb020`: 医保编码 (VARCHAR(20))
- `p_yp_code`: 药品编码 (VARCHAR(20))
- `p_yp_tj`: 给药途径 (VARCHAR(20))
- `p_dcsl`: 单次剂量 (VARCHAR(20))
- `p_gydw`: 给药单位 (VARCHAR(20))
- `p_gypc`: 给药频次 (VARCHAR(20))
- `p_csrq`: 出生日期 (VARCHAR(20), 格式: YYYYMMDD)
- `p_tz`: 体重 (VARCHAR(20))

### 主要业务逻辑
1. 检查药品是否为中药，如果是则直接返回
2. 检查用药说明是否包含"预防"且频次为"01"，如果是则直接返回
3. 检查是否有医院自定义频次配置
4. 如果有自定义频次，检查当前频次是否符合要求
5. 如果没有自定义频次，进行常规分析，检查频次是否符合药品说明书推荐

## 测试环境准备

### 基础测试数据准备SQL
```sql
-- 清理测试数据
DELETE FROM rms_itf_hos_drug WHERE drug_code LIKE '90%';
DELETE FROM rms_t_pres_med WHERE code LIKE '90%';
DELETE FROM rms_t_med_zdy_pc WHERE yp_code LIKE '90%';
DELETE FROM rms_t_pres_fx WHERE code LIKE '90%';
DELETE FROM rms_t_byyydzb WHERE yp_code LIKE '90%';
DELETE FROM rms_t_tjdzb WHERE h_tj LIKE '90%';
DELETE FROM rms_itf_hos_frequency WHERE freq_code LIKE '90%';
DELETE FROM rms_t_sda WHERE id >= 900000;
DELETE FROM rms_t_sda_cgl_condition WHERE sda_id >= 900000;
DELETE FROM rms_t_sda_cgl_result WHERE sda_id >= 900000;

-- 插入基础药品数据
INSERT INTO rms_itf_hos_drug (drug_code, drug_name, zx_flag, stop_flag) VALUES
('900001', '阿司匹林肠溶片', '1', '0'),
('900002', '板蓝根颗粒', '3', '0'),
('900003', '头孢克肟胶囊', '1', '0'),
('900004', '甘草片', '2', '0');

-- 插入处方数据
INSERT INTO rms_t_pres_med (code, yysm) VALUES
('900001', '治疗用药'),
('900002', '预防感冒'),
('900003', '抗感染治疗'),
('900004', '止咳化痰');

-- 插入频次数据
INSERT INTO rms_itf_hos_frequency (freq_code, freq_name, daily_times) VALUES
('900001', '测试每日一次', 1.00),
('900002', '测试每日两次', 2.00),
('900003', '测试每日三次', 3.00),
('900004', '测试每日四次', 4.00),
('900005', '自定义频次1', 2.00),
('900006', '自定义频次2', 3.00);

-- 插入给药途径映射数据
INSERT INTO rms_t_tjdzb (h_tj, by_code, by_name) VALUES
('900001', '01', '口服'),
('900002', '02', '静脉注射'),
('900003', '03', '肌肉注射');

-- 插入医保药品对照数据
INSERT INTO rms_t_byyydzb (akb020, yp_code, sda_id) VALUES
('A01AA01', '900001', 900001),
('A01AA02', '900003', 900002);

-- 插入标准数据
INSERT INTO rms_t_sda (id, ym, tymc) VALUES
(900001, '阿司匹林肠溶片', '阿司匹林肠溶片'),
(900002, '头孢克肟胶囊', '头孢克肟胶囊');

-- 插入常规量条件数据
INSERT INTO rms_t_sda_cgl_condition (id, sda_id, age_min, age_max, admin_routine, count_type) VALUES
(900001, 900001, 0, 36500, '01', 1),
(900002, 900002, 0, 36500, '01', 1);

-- 插入常规量结果数据
INSERT INTO rms_t_sda_cgl_result (condition_id, sda_id, reco_type, yl_min, yl_max) VALUES
(900001, 900001, 2, 1.0, 2.0),
(900002, 900002, 2, 2.0, 3.0);
```

## 测试用例

### 1. 正常情况测试（正向测试用例）

#### 测试用例1.1: 西药正常频次检查
**测试描述**: 测试西药在没有自定义频次配置时的正常频次检查  
**前置条件**: 
- 药品为西药(zx_flag='1')
- 无医院自定义频次配置
- 频次在推荐范围内

**输入参数**:
```sql
CALL rms_fx_cgl_drl(
    '900001',           -- p_code: 处方编码
    'A01AA01',          -- p_akb020: 医保编码
    '900001',           -- p_yp_code: 药品编码
    '900001',           -- p_yp_tj: 给药途径
    '100',              -- p_dcsl: 单次剂量
    'mg',               -- p_gydw: 给药单位
    '900002',           -- p_gypc: 给药频次(测试每日两次)
    '19900101',         -- p_csrq: 出生日期
    '70'                -- p_tz: 体重
);
```

**预期结果**: 
- 存储过程正常执行完成
- 不插入任何警告记录到rms_t_pres_fx表
- 频次在推荐范围内，无异常提示

**验证SQL**:
```sql
SELECT COUNT(*) as warning_count FROM rms_t_pres_fx WHERE code = '900001';
-- 预期结果: 0
```

#### 测试用例1.2: 符合医院自定义频次要求
**测试描述**: 测试药品频次符合医院自定义频次配置的情况  
**前置条件**: 
- 药品有医院自定义频次配置
- 输入频次符合自定义要求

**测试数据准备**:
```sql
-- 插入自定义频次配置
INSERT INTO rms_t_med_zdy_pc (yp_code, freq_code) VALUES ('900001', '900002');
```

**输入参数**:
```sql
CALL rms_fx_cgl_drl('900002', 'A01AA01', '900001', '900001', '100', 'mg', '900002', '19900101', '70');
```

**预期结果**: 
- 存储过程正常执行完成
- 不插入警告记录
- 直接返回，不进行后续常规分析

**验证SQL**:
```sql
SELECT COUNT(*) as warning_count FROM rms_t_pres_fx WHERE code = '900002';
-- 预期结果: 0
```

### 2. 边界条件测试

#### 测试用例2.1: 年龄边界测试
**测试描述**: 测试年龄在条件边界值时的处理  
**前置条件**: 
- 年龄刚好在age_min或age_max边界上

**输入参数**:
```sql
-- 测试新生儿(0天)
CALL rms_fx_cgl_drl('900003', 'A01AA01', '900001', '900001', '100', 'mg', '04', 
    DATE_FORMAT(CURDATE(), '%Y%m%d'), '3');

-- 测试100岁老人(36500天)
CALL rms_fx_cgl_drl('900004', 'A01AA01', '900001', '900001', '100', 'mg', '04', 
    DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 36500 DAY), '%Y%m%d'), '50');
```

**预期结果**: 
- 新生儿和老人都能正常处理
- 根据频次是否在推荐范围内决定是否产生警告

#### 测试用例2.2: 频次边界值测试
**测试描述**: 测试频次在推荐范围边界值的处理  
**前置条件**: 
- 频次刚好等于yl_min或yl_max

**测试数据准备**:
```sql
-- 更新测试数据，设置明确的边界值
UPDATE rms_t_sda_cgl_result SET yl_min = 2.0, yl_max = 3.0 WHERE sda_id = 900001;
```

**输入参数**:
```sql
-- 测试最小值边界
CALL rms_fx_cgl_drl('900005', 'A01AA01', '900001', '900001', '100', 'mg', '900002', '19900101', '70');

-- 测试最大值边界
CALL rms_fx_cgl_drl('900006', 'A01AA01', '900001', '900001', '100', 'mg', '900003', '19900101', '70');
```

**预期结果**: 
- 边界值应该被认为是合法的
- 不产生警告记录

### 3. 异常情况测试（负向测试用例）

#### 测试用例3.1: 中药直接返回测试
**测试描述**: 测试中药(zx_flag='3')时存储过程直接返回  
**前置条件**: 
- 药品为中药

**输入参数**:
```sql
CALL rms_fx_cgl_drl('900007', 'A01AA02', '900002', '900001', '10', 'g', '03', '19900101', '70');
```

**预期结果**: 
- 存储过程直接返回，不进行后续处理
- 不插入任何记录到rms_t_pres_fx表

**验证SQL**:
```sql
SELECT COUNT(*) as warning_count FROM rms_t_pres_fx WHERE code = '900007';
-- 预期结果: 0
```

#### 测试用例3.2: 预防用药且频次为01的返回测试
**测试描述**: 测试用药说明包含"预防"且频次为"01"时直接返回
**前置条件**:
- 用药说明包含"预防"
- 给药频次为"01"（存储过程中硬编码）

**测试数据准备**:
```sql
-- 需要插入系统标准频次"01"
INSERT INTO rms_itf_hos_frequency (freq_code, freq_name, daily_times) VALUES ('01', '每日一次', 1.00);
```

**输入参数**:
```sql
CALL rms_fx_cgl_drl('900002', 'A01AA01', '900001', '900001', '100', 'mg', '01', '19900101', '70');
```

**预期结果**: 
- 存储过程直接返回
- 不插入警告记录

#### 测试用例3.3: 频次不符合自定义要求测试
**测试描述**: 测试药品频次不符合医院自定义频次配置时的警告  
**前置条件**: 
- 药品有自定义频次配置
- 输入频次不在配置范围内

**测试数据准备**:
```sql
-- 清理之前的配置，插入新的自定义频次
DELETE FROM rms_t_med_zdy_pc WHERE yp_code = '900001';
INSERT INTO rms_t_med_zdy_pc (yp_code, freq_code) VALUES ('900001', '900003');
```

**输入参数**:
```sql
CALL rms_fx_cgl_drl('900008', 'A01AA01', '900001', '900001', '100', 'mg', '900004', '19900101', '70');
```

**预期结果**: 
- 插入频次不符合警告记录到rms_t_pres_fx表
- 警告类型为'YHGXHCGYFYLWT_PC'
- 包含自定义频次要求的提示信息

**验证SQL**:
```sql
SELECT * FROM rms_t_pres_fx WHERE code = '900008' AND wtlxcode = 'YHGXHCGYFYLWT_PC';
-- 预期结果: 1条记录，包含频次不符合提示
```

#### 测试用例3.4: 频次超出说明书推荐范围测试
**测试描述**: 测试频次超出药品说明书推荐范围时的警告  
**前置条件**: 
- 无自定义频次配置
- 频次超出推荐范围

**输入参数**:
```sql
-- 删除自定义频次配置，使用常规分析
DELETE FROM rms_t_med_zdy_pc WHERE yp_code = '900001';

CALL rms_fx_cgl_drl('900009', 'A01AA01', '900001', '900001', '100', 'mg', '900004', '19900101', '70');
```

**预期结果**: 
- 插入频次不符合说明书推荐的警告记录
- 警告信息包含推荐频次范围

**验证SQL**:
```sql
SELECT * FROM rms_t_pres_fx WHERE code = '900009' AND wtlxcode = 'YHGXHCGYFYLWT_PC';
-- 预期结果: 1条记录，包含说明书推荐频次提示
```

### 4. 输入参数验证测试

#### 测试用例4.1: 空值参数测试
**测试描述**: 测试关键参数为空值时的处理  

**输入参数**:
```sql
-- 测试药品编码为空
CALL rms_fx_cgl_drl('900010', 'A01AA01', NULL, '900001', '100', 'mg', '02', '19900101', '70');

-- 测试处方编码为空
CALL rms_fx_cgl_drl(NULL, 'A01AA01', '900001', '900001', '100', 'mg', '02', '19900101', '70');

-- 测试出生日期为空
CALL rms_fx_cgl_drl('900011', 'A01AA01', '900001', '900001', '100', 'mg', '02', NULL, '70');
```

**预期结果**: 
- 存储过程能够处理空值情况
- 不会产生SQL异常
- 根据业务逻辑决定是否继续处理

#### 测试用例4.2: 无效日期格式测试
**测试描述**: 测试出生日期格式不正确时的处理  

**输入参数**:
```sql
-- 测试无效日期格式
CALL rms_fx_cgl_drl('900012', 'A01AA01', '900001', '900001', '100', 'mg', '02', '1990-01-01', '70');
CALL rms_fx_cgl_drl('900013', 'A01AA01', '900001', '900001', '100', 'mg', '02', '19901301', '70');
```

**预期结果**: 
- 存储过程能够处理无效日期
- 可能触发异常处理机制
- 不会导致存储过程崩溃

### 5. 输出结果验证

#### 测试用例5.1: 警告记录完整性验证
**测试描述**: 验证插入到rms_t_pres_fx表的警告记录字段完整性  

**执行测试**:
```sql
-- 先执行一个会产生警告的调用
DELETE FROM rms_t_med_zdy_pc WHERE yp_code = '900001';
INSERT INTO rms_t_med_zdy_pc (yp_code, freq_code) VALUES ('900001', '02');
CALL rms_fx_cgl_drl('900014', 'A01AA01', '900001', '900001', '100', 'mg', '04', '19900101', '70');
```

**验证SQL**:
```sql
SELECT 
    code,
    ywa,
    ywb,
    wtlvlcode,
    wtlvlname,
    wtlxcode,
    wtlxname,
    wtms,
    jyms,
    sfqy,
    fxlx
FROM rms_t_pres_fx 
WHERE code = '900014';
```

**预期结果**: 
- code = '900014'
- ywa = 药品名称
- wtlvlcode = '1'
- wtlvlname = '一般提示'
- wtlxcode = 'YHGXHCGYFYLWT_PC'
- wtlxname = '药品用法用量'
- wtms 包含具体的警告描述
- jyms 包含建议信息
- sfqy = '0'
- fxlx = '单次常规用量分析'

## 测试执行指南

### 执行顺序
1. 首先执行基础测试数据准备SQL
2. 按照测试用例编号顺序执行
3. 每个测试用例执行后立即验证结果
4. 清理测试数据（可选）

### 清理测试数据
```sql
-- 清理所有测试数据
DELETE FROM rms_t_pres_fx WHERE code LIKE '90%';
DELETE FROM rms_t_med_zdy_pc WHERE yp_code LIKE '90%';
DELETE FROM rms_t_sda_cgl_result WHERE sda_id >= 900000;
DELETE FROM rms_t_sda_cgl_condition WHERE sda_id >= 900000;
DELETE FROM rms_t_sda WHERE id >= 900000;
DELETE FROM rms_t_byyydzb WHERE yp_code LIKE '90%';
DELETE FROM rms_t_tjdzb WHERE h_tj LIKE '90%';
DELETE FROM rms_itf_hos_frequency WHERE freq_code LIKE '90%';
DELETE FROM rms_t_pres_med WHERE code LIKE '90%';
DELETE FROM rms_itf_hos_drug WHERE drug_code LIKE '90%';
```

### 注意事项
1. 测试数据ID统一从900000开始编码
2. 每个测试用例使用独立的处方编码
3. 测试前确保相关表结构完整
4. 建议在测试环境中执行，避免影响生产数据
5. 执行测试时注意观察存储过程的异常处理机制

## 测试覆盖率分析

本测试用例覆盖了以下场景：
- ✅ 中药直接返回逻辑
- ✅ 预防用药特殊处理逻辑  
- ✅ 医院自定义频次检查逻辑
- ✅ 常规频次分析逻辑
- ✅ 年龄计算和边界处理
- ✅ 异常处理机制
- ✅ 输入参数验证
- ✅ 输出结果完整性

预计测试覆盖率: **95%以上**
