-- =====================================================
-- RMS单次常规用量分析存储过程测试脚本
-- 存储过程: rms_fx_cgl_drl
-- 创建时间: 2025-08-20
-- 当前大模型: Claude Sonnet 4
-- =====================================================

-- 设置SQL模式和字符集
SET SQL_MODE = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';
SET NAMES utf8mb4;

-- 开始测试
SELECT '=== RMS单次常规用量分析存储过程测试开始 ===' as test_info;

-- =====================================================
-- 第一步: 清理测试数据
-- =====================================================
SELECT '第一步: 清理测试数据' as step_info;

DELETE FROM rms_t_pres_fx WHERE code LIKE '90%';
DELETE FROM rms_t_med_zdy_pc WHERE yp_code LIKE '90%';
DELETE FROM rms_t_sda_cgl_result WHERE sda_id >= 900000;
DELETE FROM rms_t_sda_cgl_condition WHERE sda_id >= 900000;
DELETE FROM rms_t_sda WHERE id >= 900000;
DELETE FROM rms_t_byyydzb WHERE yp_code LIKE '90%';
DELETE FROM rms_t_tjdzb WHERE h_tj LIKE '90%';
DELETE FROM rms_itf_hos_frequency WHERE freq_code LIKE '90%';
DELETE FROM rms_t_pres_med WHERE code LIKE '90%';
DELETE FROM rms_itf_hos_drug WHERE drug_code LIKE '90%';

SELECT '测试数据清理完成' as cleanup_result;

-- =====================================================
-- 第二步: 准备基础测试数据
-- =====================================================
SELECT '第二步: 准备基础测试数据' as step_info;

-- 插入基础药品数据
INSERT INTO rms_itf_hos_drug (drug_code, drug_name, zx_flag, stop_flag) VALUES
('900001', '阿司匹林肠溶片', '1', '0'),
('900002', '板蓝根颗粒', '3', '0'),
('900003', '头孢克肟胶囊', '1', '0'),
('900004', '甘草片', '2', '0');

-- 插入处方数据
INSERT INTO rms_t_pres_med (code, yysm) VALUES
('900001', '治疗用药'),
('900002', '预防感冒'),
('900003', '抗感染治疗'),
('900004', '止咳化痰');

-- 插入频次数据
INSERT INTO rms_itf_hos_frequency (freq_code, freq_name, daily_times) VALUES
('900001', '测试每日一次', 1.00),
('900002', '测试每日两次', 2.00),
('900003', '测试每日三次', 3.00),
('900004', '测试每日四次', 4.00),
('900005', '自定义频次1', 2.00),
('900006', '自定义频次2', 3.00);

-- 插入给药途径映射数据
INSERT INTO rms_t_tjdzb (h_tj, by_code, by_name) VALUES
('900001', '01', '口服'),
('900002', '02', '静脉注射'),
('900003', '03', '肌肉注射');

-- 插入医保药品对照数据
INSERT INTO rms_t_byyydzb (akb020, yp_code, sda_id) VALUES
('A01AA01', '900001', 900001),
('A01AA02', '900003', 900002);

-- 插入标准数据
INSERT INTO rms_t_sda (id, ym, tymc) VALUES
(900001, '阿司匹林肠溶片', '阿司匹林肠溶片'),
(900002, '头孢克肟胶囊', '头孢克肟胶囊');

-- 插入常规量条件数据
INSERT INTO rms_t_sda_cgl_condition (id, sda_id, age_min, age_max, admin_routine, count_type) VALUES
(900001, 900001, 0, 36500, '01', 1),
(900002, 900002, 0, 36500, '01', 1);

-- 插入常规量结果数据
INSERT INTO rms_t_sda_cgl_result (condition_id, sda_id, reco_type, yl_min, yl_max) VALUES
(900001, 900001, 2, 1.0, 2.0),
(900002, 900002, 2, 2.0, 3.0);

SELECT '基础测试数据准备完成' as data_prep_result;

-- =====================================================
-- 第三步: 正常情况测试
-- =====================================================
SELECT '第三步: 正常情况测试' as step_info;

-- 测试用例1.1: 西药正常频次检查
SELECT '测试用例1.1: 西药正常频次检查' as test_case;
CALL rms_fx_cgl_drl('900001', 'A01AA01', '900001', '900001', '100', 'mg', '900002', '19900101', '70');

-- 验证结果
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 通过: 无警告记录，符合预期'
        ELSE CONCAT('❌ 失败: 发现', COUNT(*), '条警告记录')
    END as test_result
FROM rms_t_pres_fx WHERE code = '900001';

-- 测试用例1.2: 符合医院自定义频次要求
SELECT '测试用例1.2: 符合医院自定义频次要求' as test_case;

-- 插入自定义频次配置
INSERT INTO rms_t_med_zdy_pc (yp_code, freq_code) VALUES ('900001', '900002');

CALL rms_fx_cgl_drl('900002', 'A01AA01', '900001', '900001', '100', 'mg', '900002', '19900101', '70');

-- 验证结果
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 通过: 符合自定义频次，无警告'
        ELSE CONCAT('❌ 失败: 发现', COUNT(*), '条警告记录')
    END as test_result
FROM rms_t_pres_fx WHERE code = '900002';

-- =====================================================
-- 第四步: 边界条件测试
-- =====================================================
SELECT '第四步: 边界条件测试' as step_info;

-- 测试用例2.1: 年龄边界测试 - 新生儿
SELECT '测试用例2.1: 年龄边界测试 - 新生儿' as test_case;
SET @today = DATE_FORMAT(CURDATE(), '%Y%m%d');
SET @sql = CONCAT('CALL rms_fx_cgl_drl(''900003'', ''A01AA01'', ''900001'', ''900001'', ''100'', ''mg'', ''900004'', ''', @today, ''', ''3'')');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 测试用例2.2: 频次边界值测试
SELECT '测试用例2.2: 频次边界值测试' as test_case;

-- 更新测试数据，设置明确的边界值
UPDATE rms_t_sda_cgl_result SET yl_min = 2.0, yl_max = 3.0 WHERE sda_id = 900001;

-- 删除自定义频次配置以进行常规分析
DELETE FROM rms_t_med_zdy_pc WHERE yp_code = '900001';

-- 测试最小值边界
CALL rms_fx_cgl_drl('900005', 'A01AA01', '900001', '900001', '100', 'mg', '900002', '19900101', '70');

-- 测试最大值边界
CALL rms_fx_cgl_drl('900006', 'A01AA01', '900001', '900001', '100', 'mg', '900003', '19900101', '70');

-- 验证边界值测试结果
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 通过: 边界值测试正常'
        ELSE CONCAT('❌ 失败: 边界值测试发现', COUNT(*), '条警告')
    END as boundary_test_result
FROM rms_t_pres_fx WHERE code IN ('900005', '900006');

-- =====================================================
-- 第五步: 异常情况测试
-- =====================================================
SELECT '第五步: 异常情况测试' as step_info;

-- 测试用例3.1: 中药直接返回测试
SELECT '测试用例3.1: 中药直接返回测试' as test_case;
CALL rms_fx_cgl_drl('900007', 'A01AA02', '900002', '900001', '10', 'g', '900003', '19900101', '70');

-- 验证结果
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 通过: 中药直接返回，无警告记录'
        ELSE CONCAT('❌ 失败: 中药测试发现', COUNT(*), '条警告记录')
    END as chinese_med_test_result
FROM rms_t_pres_fx WHERE code = '900007';

-- 测试用例3.2: 预防用药且频次为01的返回测试
SELECT '测试用例3.2: 预防用药且频次为01的返回测试' as test_case;
-- 插入系统标准频次"01"
INSERT IGNORE INTO rms_itf_hos_frequency (freq_code, freq_name, daily_times) VALUES ('01', '每日一次', 1.00);
CALL rms_fx_cgl_drl('900002', 'A01AA01', '900001', '900001', '100', 'mg', '01', '19900101', '70');

-- 验证结果
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 通过: 预防用药直接返回'
        ELSE CONCAT('❌ 失败: 预防用药测试发现', COUNT(*), '条警告记录')
    END as prevention_test_result
FROM rms_t_pres_fx WHERE code = '900002' AND wtlxcode = 'YHGXHCGYFYLWT_PC';

-- 测试用例3.3: 频次不符合自定义要求测试
SELECT '测试用例3.3: 频次不符合自定义要求测试' as test_case;

-- 清理之前的配置，插入新的自定义频次
DELETE FROM rms_t_med_zdy_pc WHERE yp_code = '900001';
INSERT INTO rms_t_med_zdy_pc (yp_code, freq_code) VALUES ('900001', '900003');

CALL rms_fx_cgl_drl('900008', 'A01AA01', '900001', '900001', '100', 'mg', '900004', '19900101', '70');

-- 验证结果
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ 通过: 检测到频次不符合自定义要求'
        ELSE '❌ 失败: 未检测到频次不符合警告'
    END as custom_freq_test_result
FROM rms_t_pres_fx WHERE code = '900008' AND wtlxcode = 'YHGXHCGYFYLWT_PC';

-- 测试用例3.4: 频次超出说明书推荐范围测试
SELECT '测试用例3.4: 频次超出说明书推荐范围测试' as test_case;

-- 删除自定义频次配置，使用常规分析
DELETE FROM rms_t_med_zdy_pc WHERE yp_code = '900001';

CALL rms_fx_cgl_drl('900009', 'A01AA01', '900001', '900001', '100', 'mg', '900004', '19900101', '70');

-- 验证结果
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ 通过: 检测到频次超出说明书推荐范围'
        ELSE '❌ 失败: 未检测到频次超出警告'
    END as manual_freq_test_result
FROM rms_t_pres_fx WHERE code = '900009' AND wtlxcode = 'YHGXHCGYFYLWT_PC';

-- =====================================================
-- 第六步: 输入参数验证测试
-- =====================================================
SELECT '第六步: 输入参数验证测试' as step_info;

-- 测试用例4.1: 空值参数测试
SELECT '测试用例4.1: 空值参数测试' as test_case;

-- 测试药品编码为空
CALL rms_fx_cgl_drl('900010', 'A01AA01', NULL, '900001', '100', 'mg', '900002', '19900101', '70');

-- 测试处方编码为空
CALL rms_fx_cgl_drl(NULL, 'A01AA01', '900001', '900001', '100', 'mg', '900002', '19900101', '70');

-- 测试出生日期为空
CALL rms_fx_cgl_drl('900011', 'A01AA01', '900001', '900001', '100', 'mg', '900002', NULL, '70');

SELECT '空值参数测试完成' as null_param_test_result;

-- 测试用例4.2: 无效日期格式测试
SELECT '测试用例4.2: 无效日期格式测试' as test_case;

-- 测试无效日期格式
CALL rms_fx_cgl_drl('900012', 'A01AA01', '900001', '900001', '100', 'mg', '900002', '1990-01-01', '70');
CALL rms_fx_cgl_drl('900013', 'A01AA01', '900001', '900001', '100', 'mg', '900002', '19901301', '70');

SELECT '无效日期格式测试完成' as invalid_date_test_result;

-- =====================================================
-- 第七步: 输出结果验证
-- =====================================================
SELECT '第七步: 输出结果验证' as step_info;

-- 测试用例5.1: 警告记录完整性验证
SELECT '测试用例5.1: 警告记录完整性验证' as test_case;

-- 先执行一个会产生警告的调用
DELETE FROM rms_t_med_zdy_pc WHERE yp_code = '900001';
INSERT INTO rms_t_med_zdy_pc (yp_code, freq_code) VALUES ('900001', '900002');
CALL rms_fx_cgl_drl('900014', 'A01AA01', '900001', '900001', '100', 'mg', '900004', '19900101', '70');

-- 验证警告记录完整性
SELECT 
    code,
    ywa,
    ywb,
    wtlvlcode,
    wtlvlname,
    wtlxcode,
    wtlxname,
    wtms,
    jyms,
    sfqy,
    fxlx,
    CASE 
        WHEN code IS NOT NULL AND wtlvlcode IS NOT NULL AND wtlxcode IS NOT NULL 
        THEN '✅ 通过: 警告记录字段完整'
        ELSE '❌ 失败: 警告记录字段不完整'
    END as completeness_check
FROM rms_t_pres_fx 
WHERE code = '900014'
LIMIT 1;

-- =====================================================
-- 第八步: 测试结果汇总
-- =====================================================
SELECT '第八步: 测试结果汇总' as step_info;

-- 统计所有警告记录
SELECT 
    '总警告记录数' as metric,
    COUNT(*) as count
FROM rms_t_pres_fx 
WHERE code LIKE '90%';

-- 按警告类型统计
SELECT 
    '按警告类型统计' as summary_type,
    wtlxcode,
    wtlxname,
    COUNT(*) as count
FROM rms_t_pres_fx 
WHERE code LIKE '90%'
GROUP BY wtlxcode, wtlxname;

-- 按处方编码统计
SELECT 
    '按处方编码统计' as summary_type,
    code,
    COUNT(*) as warning_count
FROM rms_t_pres_fx 
WHERE code LIKE '90%'
GROUP BY code
ORDER BY code;

SELECT '=== RMS单次常规用量分析存储过程测试完成 ===' as test_info;
