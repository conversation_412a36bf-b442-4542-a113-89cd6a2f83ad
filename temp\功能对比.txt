// MSSQL存储过程：
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
ALTER PROCEDURE [dbo].[fx_cgl_drl]
--cglfx '380','50','0202','3','ml','一天三次（TID）','20090209','20'
--cglfx_drl '380','25','0101','7','mg','一天三次（TID）','20170209','30'
--fx_cgl_drl '552adf84-47e6-4ae7-a61f-87e98ef5a623','380','259960','Y363','1.0000','g','01','20000101','50'
---单次常规用量分析
@Code nvarchar(50),
@akb020  nvarchar(20),
@yp_code nvarchar(20),
@yp_tj    nvarchar(20),
@dcsl    nvarchar(20),
@gydw     nvarchar(20),
@gypc    nvarchar(20),
@csrq    nvarchar(20),
@tz   nvarchar(20)----体重
AS

declare @sda_id nvarchar(20)
declare @sda_tj nvarchar(20)
declare @sda_drcs nvarchar(20)
declare @sda_dcyl nvarchar(20)
declare @nl nvarchar(20)
declare @condition_id nvarchar(20)
declare @count_type nvarchar(20)
declare @n_count int
declare @n_count1 int
declare @yysm nvarchar(50)
declare @zx_flag nvarchar(50)

BEGIN
--return

declare @ywa_name nvarchar(50)
select @ywa_name=DRUG_NAME,@zx_flag=ZX_FLAG from ITF_HOS_DRUG where DRUG_CODE =@yp_code

if @zx_flag ='3'
begin
return
end


select @yysm=yysm from t_pres_med where Code =@Code

if @yysm like '%预防%' and @gypc ='01'
begin
return
end



select @n_count=count(1) from t_med_zdy_pc  where   yp_code=@yp_code;
if @n_count>0
begin
select @n_count1=count(1) from  t_med_zdy_pc  where yp_code=@yp_code and freq_code =  @gypc;
if @n_count1 <=0
begin
select distinct @Code, @ywa_name ywa,'' ywb,'1'  wtlvlcode,
  '一般提示'  wtlvl,
  'RLT030'  wtcode,
  'YHGXHCGYFYLWT_PC'  wtsp,
  '药品用法用量' wtname,
  CAST(@ywa_name as varchar)+'++++' +'频次不符合医院自定义频次'  title,
  '医院自定义要求该药频次为：'+ dbo.get_zongzhuanheng(@yp_code)  as detail
,0,'单次常规用量分析' from t_med_zdy_pc a
where  a.yp_code =@yp_code;
end
else
begin
return;
end

end
if @n_count=0
begin

select @sda_id=sda_id from t_byyydzb  where akb020 =@akb020 and yp_code=@yp_code;
select distinct @sda_tj= SUBSTRING(by_code,1,2) from t_tjdzb where h_tj= @yp_tj;
--select @sda_dcyl=minjl*cast(@dcsl as decimal) from T_HIS_DRUG where ypcode = @yp_code and unit=@gydw;
select @sda_drcs=daily_times from ITF_HOS_FREQUENCY where freq_code =@gypc ;
select  @nl=DATEDIFF(dd,@csrq,GETDATE()) ;

--select @condition_id='49133';

select @condition_id=id,@count_type=count_type from t_sda_cgl_condition
where sda_id =@sda_id
and admin_routine like '%'+@sda_tj+'%'
and age_min <@nl
and age_max >@nl;



begin
----单次超极量分析
select @Code, @ywa_name ywa,'' ywb,'1'  wtlvlcode,
  '一般提示'  wtlvl,
  'RLT030'  wtcode,
  'YHGXHCGYFYLWT_PC'  wtsp,
  '药品用法用量' wtname,
  CAST(c.tymc as varchar)+'++++' +'频次不符合药品说明书推荐频次'  title,
  '说明书提示：'+ CAST(c.tymc as varchar)+'++++'+'频次为：' +cast(a.yl_min as varchar)+'~'+cast(a.yl_max as varchar)  as detail
,0,'单次常规用量分析' from t_sda c left join t_sda_cgl_result a on  a.sda_id =c.ID   where c.ID =@sda_id and a.condition_id =@condition_id
and reco_type ='2'
and (a.yl_min>@sda_drcs or a.yl_max <@sda_drcs)
end
end

END










// MySQL存储过程：
CREATE DEFINER=`root`@`%` PROCEDURE `rms_fx_cgl_drl`(
    IN p_code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_yp_tj VARCHAR(20),
    IN p_dcsl VARCHAR(20),
    IN p_gydw VARCHAR(20),
    IN p_gypc VARCHAR(20),
    IN p_csrq VARCHAR(20),
    IN p_tz VARCHAR(20)
)
    COMMENT '单次常规用量分析存储过程'
main_block: BEGIN
		-- 声明变量
		DECLARE v_sda_id VARCHAR(20);
		DECLARE v_sda_tj VARCHAR(20);
		DECLARE v_sda_drcs VARCHAR(20);
		DECLARE v_sda_dcyl VARCHAR(20);
		DECLARE v_nl VARCHAR(20);
		DECLARE v_condition_id VARCHAR(20);
		DECLARE v_count_type VARCHAR(20);
		DECLARE v_n_count INT DEFAULT 0;
		DECLARE v_n_count1 INT DEFAULT 0;
		DECLARE v_yysm VARCHAR(50);
		DECLARE v_zx_flag VARCHAR(50);
		DECLARE v_ywa_name VARCHAR(50);
		DECLARE v_freq_times VARCHAR(10);
		DECLARE v_zongzhuanheng VARCHAR(200);

		-- 声明异常处理
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
		BEGIN
				GET DIAGNOSTICS CONDITION 1
            @sqlstate = RETURNED_SQLSTATE,
            @errno = MYSQL_ERRNO,
            @text = MESSAGE_TEXT;
				select CONCAT('执行异常: ', @sqlstate, @errno, @text);
		END;

		-- 获取药品基本信息
		SELECT DRUG_NAME, ZX_FLAG INTO v_ywa_name, v_zx_flag
		FROM rms_itf_hos_drug
		WHERE DRUG_CODE = p_yp_code LIMIT 1;

		-- 如果是中药则返回
		IF v_zx_flag = '3' THEN
				LEAVE main_block;
		END IF;

		-- 获取用药说明
		SELECT yysm INTO v_yysm
		FROM rms_t_pres_med
		WHERE Code = p_code LIMIT 1;

		-- 如果用药说明包含"预防"且给药频次是"01"则返回
		IF v_yysm LIKE '%预防%' AND p_gypc = '01' THEN
				LEAVE main_block;
		END IF;

		-- 检查是否有医院自定义频次
		SELECT COUNT(1) INTO v_n_count
		FROM rms_t_med_zdy_pc
		WHERE yp_code = p_yp_code;

		IF v_n_count > 0 THEN
				-- 检查频次是否符合自定义要求
				SELECT COUNT(1) INTO v_n_count1
				FROM rms_t_med_zdy_pc
				WHERE yp_code = p_yp_code AND freq_code = p_gypc;

				IF v_n_count1 <= 0 THEN
						-- 获取自定义频次信息（这里简化处理，实际应该实现get_zongzhuanheng函数）
						SELECT GROUP_CONCAT(DISTINCT freq_code) INTO v_zongzhuanheng
						FROM rms_t_med_zdy_pc
						WHERE yp_code = p_yp_code;

						-- 插入频次不符合提示
						INSERT INTO rms_t_pres_fx
						SELECT p_code, v_ywa_name, '', '1', '一般提示', 'RLT030', 'YHGXHCGYFYLWT_PC',
								'药品用法用量', CONCAT(v_ywa_name, '++++', '频次不符合医院自定义频次'),
								CONCAT('医院自定义要求该药频次为：', IFNULL(v_zongzhuanheng, '')),
								'0', '单次常规用量分析';
				ELSE
						-- 符合自定义频次要求，直接返回
						LEAVE main_block;
				END IF;
		END IF;

		-- 如果没有自定义频次，进行常规分析
		IF v_n_count = 0 THEN
				-- 获取SDA ID
				SELECT sda_id INTO v_sda_id
				FROM rms_t_byyydzb
				WHERE akb020 = p_akb020 AND yp_code = p_yp_code LIMIT 1;

				-- 获取给药途径编码
				SELECT DISTINCT SUBSTRING(by_code, 1, 2) INTO v_sda_tj
				FROM rms_t_tjdzb
				WHERE h_tj = p_yp_tj LIMIT 1;

				-- 获取每日次数
				SELECT daily_times INTO v_sda_drcs
				FROM rms_itf_hos_frequency
				WHERE freq_code = p_gypc LIMIT 1;

				-- 计算年龄（天数）
				SET v_nl = DATEDIFF(CURDATE(), STR_TO_DATE(p_csrq, '%Y%m%d'));

				-- 获取条件ID和计算类型
				SELECT id, count_type INTO v_condition_id, v_count_type
				FROM rms_t_sda_cgl_condition
				WHERE sda_id = v_sda_id
				AND admin_routine LIKE CONCAT('%', v_sda_tj, '%')
				AND age_min < v_nl
				AND age_max > v_nl
				LIMIT 1;

				-- 进行单次超极量分析
				IF v_condition_id IS NOT NULL THEN
						INSERT INTO rms_t_pres_fx
						SELECT p_code, v_ywa_name, '', '1', '一般提示', 'RLT030', 'YHGXHCGYFYLWT_PC',
								'药品用法用量',
								CONCAT(c.tymc, '++++', '频次不符合药品说明书推荐频次'),
								CONCAT('说明书提示：', c.tymc, '++++', '频次为：',
												CAST(a.yl_min AS CHAR), '~', CAST(a.yl_max AS CHAR)),
								'0', '单次常规用量分析'
						FROM rms_t_sda c
						LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID
						WHERE c.ID = v_sda_id
						AND a.condition_id = v_condition_id
						AND reco_type = '2'
						AND (a.yl_min > CAST(v_sda_drcs AS SIGNED) OR a.yl_max < CAST(v_sda_drcs AS SIGNED));
				END IF;
		END IF;

END
