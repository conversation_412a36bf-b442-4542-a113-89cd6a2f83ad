-- =====================================================
-- RMS单次常规用量分析存储过程快速测试脚本
-- 存储过程: rms_fx_cgl_drl
-- 用途: 快速验证核心功能是否正常
-- 当前大模型: <PERSON> 4
-- =====================================================

-- 设置SQL模式
SET SQL_MODE = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';
SET NAMES utf8mb4;

SELECT '=== RMS单次常规用量分析存储过程快速测试 ===' as test_info;

-- =====================================================
-- 快速数据准备
-- =====================================================

-- 清理测试数据
DELETE FROM rms_t_pres_fx WHERE code LIKE 'QT%';
DELETE FROM rms_t_med_zdy_pc WHERE yp_code = 'QT001';
DELETE FROM rms_t_sda_cgl_result WHERE sda_id = 900100;
DELETE FROM rms_t_sda_cgl_condition WHERE sda_id = 900100;
DELETE FROM rms_t_sda WHERE id = 900100;
DELETE FROM rms_t_byyydzb WHERE yp_code = 'QT001';
DELETE FROM rms_t_tjdzb WHERE h_tj = 'QT001';
DELETE FROM rms_itf_hos_frequency WHERE freq_code LIKE '9001%';
DELETE FROM rms_t_pres_med WHERE code LIKE 'QT%';
DELETE FROM rms_itf_hos_drug WHERE drug_code IN ('QT001', 'QT002');

-- 插入快速测试数据
INSERT INTO rms_itf_hos_drug (drug_code, drug_name, zx_flag, stop_flag) VALUES
('QT001', '测试西药', '1', '0'),
('QT002', '测试中药', '3', '0');

INSERT INTO rms_t_pres_med (code, yysm) VALUES
('QT001', '治疗用药'),
('QT002', '预防用药');

INSERT INTO rms_itf_hos_frequency (freq_code, freq_name, daily_times) VALUES
('900101', '测试每日一次', 1.00),
('900102', '测试每日两次', 2.00);

INSERT INTO rms_t_tjdzb (h_tj, by_code, by_name) VALUES
('QT001', '01', '口服');

INSERT INTO rms_t_byyydzb (akb020, yp_code, sda_id) VALUES
('QT001', 'QT001', 900100);

INSERT INTO rms_t_sda (id, ym, tymc) VALUES
(900100, '测试西药', '测试西药');

INSERT INTO rms_t_sda_cgl_condition (id, sda_id, age_min, age_max, admin_routine, count_type) VALUES
(900100, 900100, 0, 36500, '01', 1);

INSERT INTO rms_t_sda_cgl_result (condition_id, sda_id, reco_type, yl_min, yl_max) VALUES
(900100, 900100, 2, 1.0, 2.0);

SELECT '快速测试数据准备完成' as prep_status;

-- =====================================================
-- 核心功能测试
-- =====================================================

-- 测试1: 中药直接返回
SELECT '测试1: 中药直接返回' as test_name;
CALL rms_fx_cgl_drl('QT001', 'QT001', 'QT002', 'QT001', '10', 'g', '900101', '19900101', '70');

SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 通过'
        ELSE '❌ 失败'
    END as result,
    '中药应该直接返回，不产生警告' as description
FROM rms_t_pres_fx WHERE code = 'QT001';

-- 测试2: 预防用药且频次01直接返回
SELECT '测试2: 预防用药且频次01直接返回' as test_name;
-- 插入系统标准频次"01"
INSERT IGNORE INTO rms_itf_hos_frequency (freq_code, freq_name, daily_times) VALUES ('01', '每日一次', 1.00);
CALL rms_fx_cgl_drl('QT002', 'QT001', 'QT001', 'QT001', '100', 'mg', '01', '19900101', '70');

SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 通过'
        ELSE '❌ 失败'
    END as result,
    '预防用药且频次01应该直接返回' as description
FROM rms_t_pres_fx WHERE code = 'QT002';

-- 测试3: 符合自定义频次
SELECT '测试3: 符合自定义频次' as test_name;
INSERT INTO rms_t_med_zdy_pc (yp_code, freq_code) VALUES ('QT001', '900102');
CALL rms_fx_cgl_drl('QT003', 'QT001', 'QT001', 'QT001', '100', 'mg', '900102', '19900101', '70');

SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 通过'
        ELSE '❌ 失败'
    END as result,
    '符合自定义频次应该不产生警告' as description
FROM rms_t_pres_fx WHERE code = 'QT003';

-- 测试4: 不符合自定义频次
SELECT '测试4: 不符合自定义频次' as test_name;
CALL rms_fx_cgl_drl('QT004', 'QT001', 'QT001', 'QT001', '100', 'mg', '900101', '19900101', '70');

SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ 通过'
        ELSE '❌ 失败'
    END as result,
    '不符合自定义频次应该产生警告' as description
FROM rms_t_pres_fx WHERE code = 'QT004';

-- 测试5: 常规分析 - 频次在推荐范围内
SELECT '测试5: 常规分析 - 频次在推荐范围内' as test_name;
DELETE FROM rms_t_med_zdy_pc WHERE yp_code = 'QT001';
CALL rms_fx_cgl_drl('QT005', 'QT001', 'QT001', 'QT001', '100', 'mg', '900102', '19900101', '70');

SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 通过'
        ELSE '❌ 失败'
    END as result,
    '频次在推荐范围内应该不产生警告' as description
FROM rms_t_pres_fx WHERE code = 'QT005';

-- 测试6: 常规分析 - 频次超出推荐范围
SELECT '测试6: 常规分析 - 频次超出推荐范围' as test_name;

-- 插入一个每日4次的频次
INSERT INTO rms_itf_hos_frequency (freq_code, freq_name, daily_times) VALUES ('900104', '测试每日四次', 4.00);

CALL rms_fx_cgl_drl('QT006', 'QT001', 'QT001', 'QT001', '100', 'mg', '900104', '19900101', '70');

SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ 通过'
        ELSE '❌ 失败'
    END as result,
    '频次超出推荐范围应该产生警告' as description
FROM rms_t_pres_fx WHERE code = 'QT006';

-- =====================================================
-- 测试结果汇总
-- =====================================================

SELECT '=== 快速测试结果汇总 ===' as summary_title;

-- 统计测试结果
SELECT 
    '总测试用例数' as metric,
    6 as total_cases;

SELECT 
    '产生警告的测试用例' as metric,
    COUNT(DISTINCT code) as warning_cases
FROM rms_t_pres_fx 
WHERE code LIKE 'QT%';

-- 显示所有警告记录
SELECT 
    '警告记录详情' as detail_title,
    code as test_case,
    ywa as drug_name,
    wtlxname as warning_type,
    wtms as warning_message
FROM rms_t_pres_fx 
WHERE code LIKE 'QT%'
ORDER BY code;

-- 预期结果说明
SELECT '=== 预期结果说明 ===' as expected_title;
SELECT 'QT004: 不符合自定义频次 - 应该有警告' as expected_1;
SELECT 'QT006: 频次超出推荐范围 - 应该有警告' as expected_2;
SELECT '其他测试用例: 应该无警告' as expected_3;

-- 清理快速测试数据
DELETE FROM rms_t_pres_fx WHERE code LIKE 'QT%';
DELETE FROM rms_t_med_zdy_pc WHERE yp_code = 'QT001';
DELETE FROM rms_t_sda_cgl_result WHERE sda_id = 900100;
DELETE FROM rms_t_sda_cgl_condition WHERE sda_id = 900100;
DELETE FROM rms_t_sda WHERE id = 900100;
DELETE FROM rms_t_byyydzb WHERE yp_code = 'QT001';
DELETE FROM rms_t_tjdzb WHERE h_tj = 'QT001';
DELETE FROM rms_itf_hos_frequency WHERE freq_code LIKE '9001%';
DELETE FROM rms_t_pres_med WHERE code LIKE 'QT%';
DELETE FROM rms_itf_hos_drug WHERE drug_code LIKE 'QT%';

SELECT '快速测试数据清理完成' as cleanup_status;
SELECT '=== 快速测试完成 ===' as test_complete;
