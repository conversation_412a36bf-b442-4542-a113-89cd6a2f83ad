# RMS单次常规用量分析存储过程测试执行指南

## 概述

本指南提供了对`rms_fx_cgl_drl`存储过程进行全面测试的详细步骤和说明。

**当前大模型**: Claude Sonnet 4

## 文件说明

### 测试文件清单
1. **rms_fx_cgl_drl_测试用例.md** - 详细的测试用例文档
2. **rms_fx_cgl_drl_test_script.sql** - 完整的测试脚本
3. **rms_fx_cgl_drl_quick_test.sql** - 快速验证脚本
4. **测试执行指南.md** - 本文档

### 存储过程功能概述
`rms_fx_cgl_drl`存储过程主要功能：
- 检查药品给药频次是否符合医院自定义频次配置
- 验证频次是否符合药品说明书推荐范围
- 对中药和预防用药进行特殊处理
- 生成相应的警告信息

## 测试环境要求

### 数据库环境
- MySQL 5.7+ 或 MySQL 8.0+
- 确保所有相关表结构完整
- 建议在测试环境中执行，避免影响生产数据

### 必需的表结构
确保以下表存在且结构正确：
- `rms_itf_hos_drug` - 医院药品信息表
- `rms_t_pres_med` - 处方药品表
- `rms_t_med_zdy_pc` - 医院自定义频次表
- `rms_t_pres_fx` - 处方分析结果表
- `rms_t_byyydzb` - 医保药品对照表
- `rms_t_tjdzb` - 给药途径对照表
- `rms_itf_hos_frequency` - 频次信息表
- `rms_t_sda_cgl_condition` - 常规量条件表
- `rms_t_sda_cgl_result` - 常规量结果表
- `rms_t_sda` - 标准数据表

### 权限要求
- 对上述表的SELECT、INSERT、UPDATE、DELETE权限
- 执行存储过程的权限

## 测试执行步骤

### 方式一：快速验证（推荐新手）

1. **执行快速测试脚本**
   ```sql
   SOURCE temp/rms_fx_cgl_drl_quick_test.sql;
   ```

2. **查看测试结果**
   - 脚本会自动显示每个测试用例的执行结果
   - ✅ 表示测试通过
   - ❌ 表示测试失败

3. **预期结果**
   - 总共6个测试用例
   - 其中2个应该产生警告（QT004, QT006）
   - 其他4个不应该产生警告

   **注意**:
   - 大部分测试数据都使用900000+的编码，避免与生产数据冲突
   - 某些测试用例需要使用系统标准编码（如频次"01"），因为存储过程中有硬编码逻辑

### 方式二：完整测试（推荐专业测试）

1. **执行完整测试脚本**
   ```sql
   SOURCE temp/rms_fx_cgl_drl_test_script.sql;
   ```

2. **逐步验证结果**
   - 脚本会分步骤执行所有测试用例
   - 每个步骤都会显示执行状态和结果
   - 可以根据输出信息判断测试是否通过

3. **手动验证关键结果**
   ```sql
   -- 查看所有警告记录
   SELECT * FROM rms_t_pres_fx WHERE code LIKE '90%';
   
   -- 统计警告类型
   SELECT wtlxcode, wtlxname, COUNT(*) 
   FROM rms_t_pres_fx 
   WHERE code LIKE '90%' 
   GROUP BY wtlxcode, wtlxname;
   ```

### 方式三：手动逐个测试

1. **参考测试用例文档**
   - 打开`rms_fx_cgl_drl_测试用例.md`
   - 按照文档中的测试用例逐个执行

2. **准备测试数据**
   ```sql
   -- 执行基础测试数据准备SQL（见测试用例文档）
   ```

3. **逐个执行测试用例**
   - 按照文档中的顺序执行每个测试用例
   - 验证每个测试用例的预期结果

## 测试用例覆盖范围

### 正常情况测试
- ✅ 西药正常频次检查
- ✅ 符合医院自定义频次要求

### 边界条件测试
- ✅ 年龄边界测试（新生儿、老人）
- ✅ 频次边界值测试（最小值、最大值）

### 异常情况测试
- ✅ 中药直接返回逻辑
- ✅ 预防用药特殊处理
- ✅ 频次不符合自定义要求
- ✅ 频次超出说明书推荐范围

### 输入参数验证
- ✅ 空值参数处理
- ✅ 无效日期格式处理

### 输出结果验证
- ✅ 警告记录完整性检查
- ✅ 警告信息准确性验证

## 常见问题及解决方案

### 问题1：测试数据冲突
**现象**: 执行测试时出现主键冲突或数据重复错误

**解决方案**:
```sql
-- 清理所有测试数据
DELETE FROM rms_t_pres_fx WHERE code LIKE '90%' OR code LIKE 'QT%';
DELETE FROM rms_t_med_zdy_pc WHERE yp_code LIKE '90%' OR yp_code LIKE 'QT%';
-- ... 其他清理语句
```

### 问题2：存储过程不存在
**现象**: 调用存储过程时提示"PROCEDURE doesn't exist"

**解决方案**:
1. 确认存储过程已正确创建
2. 检查存储过程名称拼写
3. 确认当前数据库连接正确

### 问题3：表结构不匹配
**现象**: 执行过程中出现字段不存在错误

**解决方案**:
1. 检查数据库结构文件是否完整导入
2. 确认表结构与存储过程中使用的字段一致
3. 必要时重新导入数据库结构

### 问题4：权限不足
**现象**: 执行时提示权限错误

**解决方案**:
```sql
-- 授予必要权限（需要管理员执行）
GRANT SELECT, INSERT, UPDATE, DELETE ON database_name.* TO 'username'@'host';
GRANT EXECUTE ON database_name.* TO 'username'@'host';
FLUSH PRIVILEGES;
```

## 测试结果分析

### 成功标准
- 所有正常情况测试不产生警告记录
- 所有异常情况测试产生预期的警告记录
- 边界条件测试按预期处理
- 输入参数验证能正确处理异常输入
- 输出结果格式正确且信息完整

### 失败分析
如果测试失败，按以下步骤分析：

1. **检查测试数据**
   - 确认基础数据是否正确插入
   - 验证数据关联关系是否正确

2. **检查存储过程逻辑**
   - 对比存储过程代码与预期逻辑
   - 检查条件判断是否正确

3. **检查数据库环境**
   - 确认表结构完整性
   - 检查数据类型匹配

4. **查看详细错误信息**
   ```sql
   -- 查看MySQL错误日志
   SHOW VARIABLES LIKE 'log_error';
   
   -- 查看警告信息
   SHOW WARNINGS;
   ```

## 性能测试建议

### 基础性能测试
```sql
-- 测试执行时间
SET @start_time = NOW(6);
CALL rms_fx_cgl_drl('PERF001', 'A01AA01', '900001', '900001', '100', 'mg', '02', '19900101', '70');
SELECT TIMESTAMPDIFF(MICROSECOND, @start_time, NOW(6)) as execution_time_microseconds;
```

### 批量性能测试
```sql
-- 批量执行测试
DELIMITER $$
CREATE PROCEDURE batch_performance_test()
BEGIN
    DECLARE i INT DEFAULT 1;
    DECLARE start_time TIMESTAMP(6);
    
    SET start_time = NOW(6);
    
    WHILE i <= 1000 DO
        CALL rms_fx_cgl_drl(CONCAT('PERF', LPAD(i, 6, '0')), 'A01AA01', '900001', '900001', '100', 'mg', '02', '19900101', '70');
        SET i = i + 1;
    END WHILE;
    
    SELECT TIMESTAMPDIFF(MICROSECOND, start_time, NOW(6)) as total_time_microseconds,
           TIMESTAMPDIFF(MICROSECOND, start_time, NOW(6)) / 1000 as avg_time_microseconds;
END$$
DELIMITER ;

-- 执行批量测试
CALL batch_performance_test();

-- 清理性能测试数据
DELETE FROM rms_t_pres_fx WHERE code LIKE 'PERF%';
DROP PROCEDURE batch_performance_test;
```

## 测试报告模板

### 测试执行记录
- **测试日期**: ___________
- **测试环境**: ___________
- **数据库版本**: ___________
- **执行人员**: ___________

### 测试结果统计
- **总测试用例数**: ___________
- **通过用例数**: ___________
- **失败用例数**: ___________
- **通过率**: ___________%

### 问题记录
| 测试用例 | 问题描述 | 严重程度 | 状态 |
|---------|---------|---------|------|
|         |         |         |      |

### 建议和结论
- **整体评估**: ___________
- **改进建议**: ___________
- **是否可以上线**: ___________

## 维护说明

### 测试数据维护
- 测试数据ID统一从900000开始编码
- 快速测试使用QT前缀
- 定期清理测试数据，避免影响生产环境

### 测试用例更新
- 当存储过程逻辑变更时，及时更新测试用例
- 新增功能时，补充相应的测试用例
- 定期review测试用例的有效性

### 文档维护
- 保持测试文档与实际代码同步
- 记录重要的测试发现和解决方案
- 定期更新测试执行指南

---

**注意**: 本测试指南基于当前的存储过程版本编写，如果存储过程有更新，请相应更新测试用例和指南内容。
